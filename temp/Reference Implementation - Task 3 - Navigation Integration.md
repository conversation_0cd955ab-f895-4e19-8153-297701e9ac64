# Reference Implementation: Task 3 - Navigation Integration

## Overview
This document provides a complete reference implementation for integrating authentication into the app's navigation system. This involves a major restructuring of AppShell.kt and adding new authentication routes to create a seamless authentication flow.

## File Structure
```
app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/navigation/
├── AppRoute.kt (modified)

app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/
├── AppShell.kt (major restructure)
└── auth/ (screens from Task 2)
```

## Implementation

### 1. Updated AppRoute.kt
```kotlin
package eu.torvian.chatbot.app.domain.navigation

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Defines the contract for application navigation routes.
 *
 * @property name The name of the route, used for serialization.
 * @property route The actual route string, used for navigation.
 */
interface AppRoute {
    val name: String
    val route: String
}

// --- Authentication Routes ---

/**
 * Route for the authentication loading/splash screen.
 */
@Serializable
@SerialName("auth_loading")
object AuthLoading : AppRoute {
    override val name = "auth_loading"
    override val route = "auth_loading"
}

/**
 * Route for the login screen.
 */
@Serializable
@SerialName("login")
object Login : AppRoute {
    override val name = "login"
    override val route = "login"
}

/**
 * Route for the registration screen.
 */
@Serializable
@SerialName("register")
object Register : AppRoute {
    override val name = "register"
    override val route = "register"
}

// --- Main Application Routes ---

/**
 * Route for the main chat interface.
 */
@Serializable
@SerialName("chat")
object Chat : AppRoute {
    override val name = "chat"
    override val route = "chat"
}

/**
 * Route for the settings configuration interface.
 */
@Serializable
@SerialName("settings")
object Settings : AppRoute {
    override val name = "settings"
    override val route = "settings"
}

// Future routes can be added here, potentially with arguments:
// @Serializable
// @SerialName("user_profile")
// data class UserProfile(val userId: Long) : AppRoute {
//     override val name = "user_profile"
//     override val route = "user_profile/$userId"
// }
```

### 2. Completely Restructured AppShell.kt
```kotlin
package eu.torvian.chatbot.app.compose

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountCircle
import androidx.compose.material.icons.filled.ExitToApp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import eu.torvian.chatbot.app.compose.auth.*
import eu.torvian.chatbot.app.compose.settings.SettingsScreen
import eu.torvian.chatbot.app.domain.events.AppError
import eu.torvian.chatbot.app.domain.events.SnackbarInteractionEvent
import eu.torvian.chatbot.app.domain.navigation.*
import eu.torvian.chatbot.app.repository.AuthState
import eu.torvian.chatbot.app.service.misc.EventBus
import eu.torvian.chatbot.app.viewmodel.SessionListViewModel
import eu.torvian.chatbot.app.viewmodel.auth.AuthViewModel
import eu.torvian.chatbot.app.viewmodel.chat.ChatViewModel
import kotlinx.coroutines.launch
import org.koin.compose.currentKoinScope
import org.koin.compose.koinViewModel

/**
 * The main application shell responsible for top-level layout and authentication-aware navigation.
 * 
 * This component handles:
 * - Authentication state checking and routing
 * - Conditional navigation between auth flow and main app
 * - Global error handling and snackbar display
 * - User context management
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppShell() {
    val authViewModel: AuthViewModel = koinViewModel()
    val authState by authViewModel.authState.collectAsState()
    val eventBus: EventBus = currentKoinScope().get()
    val snackbarHostState = remember { SnackbarHostState() }
    val scope = rememberCoroutineScope()

    // Check initial authentication state on app startup
    LaunchedEffect(Unit) {
        authViewModel.checkInitialAuthState()
    }

    // Global error handling
    LaunchedEffect(eventBus) {
        eventBus.events.collect { event ->
            if (event is AppError) {
                val message = event.userMessage ?: event.technicalMessage
                val actionLabel = if (event.isRetryable) "Retry" else null
                val duration = if (event is AppError) SnackbarDuration.Long else SnackbarDuration.Short

                // Show the Snackbar with custom visuals
                val visuals = SnackbarVisualsWithError(
                    isError = event is AppError,
                    message = message,
                    actionLabel = actionLabel,
                    duration = duration
                )
                val result = snackbarHostState.showSnackbar(visuals)

                // Emit a new event to communicate the Snackbar interaction back to ViewModels
                eventBus.emitEvent(
                    SnackbarInteractionEvent(
                        originalAppEventId = event.eventId,
                        isActionPerformed = result == SnackbarResult.ActionPerformed
                    )
                )
            }
        }
    }

    // Route based on authentication state
    when (authState) {
        is AuthState.Loading -> {
            AuthLoadingScreen()
        }
        
        is AuthState.Unauthenticated -> {
            AuthenticationFlow(
                snackbarHostState = snackbarHostState,
                authViewModel = authViewModel
            )
        }
        
        is AuthState.Authenticated -> {
            MainApplicationFlow(
                authState = authState,
                snackbarHostState = snackbarHostState,
                authViewModel = authViewModel
            )
        }
    }
}

/**
 * Authentication flow navigation for unauthenticated users.
 */
@Composable
private fun AuthenticationFlow(
    snackbarHostState: SnackbarHostState,
    authViewModel: AuthViewModel
) {
    val navController = rememberNavController()

    Scaffold(
        snackbarHost = {
            SnackbarHost(hostState = snackbarHostState) { data ->
                val visualsWithError = data.visuals as? SnackbarVisualsWithError
                AuthSnackbar(data, visualsWithError)
            }
        }
    ) { paddingValues ->
        Box(modifier = Modifier.fillMaxSize().padding(paddingValues)) {
            NavHost(
                navController = navController, 
                startDestination = Login
            ) {
                composable<Login> {
                    LoginScreen(
                        onNavigateToRegister = {
                            navController.navigate(Register) {
                                // Don't add to back stack to prevent back navigation to login
                                popUpTo(Login) { inclusive = true }
                            }
                        },
                        authViewModel = authViewModel
                    )
                }
                
                composable<Register> {
                    RegisterScreen(
                        onNavigateToLogin = {
                            navController.navigate(Login) {
                                popUpTo(Register) { inclusive = true }
                            }
                        },
                        onRegistrationSuccess = {
                            navController.navigate(Login) {
                                popUpTo(Register) { inclusive = true }
                            }
                        },
                        authViewModel = authViewModel
                    )
                }
            }
        }
    }
}

/**
 * Main application flow for authenticated users.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MainApplicationFlow(
    authState: AuthState.Authenticated,
    snackbarHostState: SnackbarHostState,
    authViewModel: AuthViewModel
) {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route
    val sessionListViewModel: SessionListViewModel = koinViewModel()
    val chatViewModel: ChatViewModel = koinViewModel()
    val scope = rememberCoroutineScope()

    // Load initial data for authenticated user
    LaunchedEffect(authState.userId) {
        sessionListViewModel.loadSessionsAndGroups()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text("Chatbot - ${authState.username}") 
                },
                actions = {
                    UserMenu(
                        username = authState.username,
                        onLogout = { authViewModel.logout() }
                    )
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                )
            )
        },
        bottomBar = {
            NavigationBar(
                modifier = Modifier.fillMaxWidth()
                    .background(MaterialTheme.colorScheme.primaryContainer)
            ) {
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Chat, contentDescription = "Chat") },
                    label = { Text("Chat") },
                    selected = currentRoute == Chat.name,
                    onClick = {
                        navController.navigate(Chat) {
                            popUpTo(navController.graph.startDestinationId) {
                                saveState = true
                            }
                            launchSingleTop = true
                            restoreState = true
                        }
                    }
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Settings, contentDescription = "Settings") },
                    label = { Text("Settings") },
                    selected = currentRoute == Settings.name,
                    onClick = {
                        navController.navigate(Settings) {
                            popUpTo(navController.graph.startDestinationId) {
                                saveState = true
                            }
                            launchSingleTop = true
                            restoreState = true
                        }
                    }
                )
            }
        },
        snackbarHost = {
            SnackbarHost(hostState = snackbarHostState) { data ->
                val visualsWithError = data.visuals as? SnackbarVisualsWithError
                MainAppSnackbar(data, visualsWithError)
            }
        }
    ) { paddingValues ->
        Box(modifier = Modifier.fillMaxSize().padding(paddingValues)) {
            NavHost(navController = navController, startDestination = Chat) {
                composable<Chat> {
                    ChatScreen(sessionListViewModel, chatViewModel)
                }
                composable<Settings> {
                    SettingsScreen()
                }
            }
        }
    }
}

/**
 * User menu dropdown for authenticated users.
 */
@Composable
private fun UserMenu(
    username: String,
    onLogout: () -> Unit
) {
    var expanded by remember { mutableStateOf(false) }

    Box {
        IconButton(onClick = { expanded = true }) {
            Icon(
                imageVector = Icons.Default.AccountCircle,
                contentDescription = "User menu"
            )
        }
        
        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            // User info header
            DropdownMenuItem(
                text = { 
                    Text(
                        text = username,
                        style = MaterialTheme.typography.titleMedium
                    ) 
                },
                onClick = { /* Future: navigate to profile */ },
                leadingIcon = {
                    Icon(Icons.Default.AccountCircle, contentDescription = null)
                }
            )
            
            Divider()
            
            // Logout option
            DropdownMenuItem(
                text = { Text("Logout") },
                onClick = {
                    expanded = false
                    onLogout()
                },
                leadingIcon = {
                    Icon(Icons.Default.ExitToApp, contentDescription = null)
                }
            )
        }
    }
}
```

### 3. Snackbar Components
```kotlin
// Add these to the AppShell.kt file or create a separate SnackbarComponents.kt

/**
 * Custom snackbar visuals that include error state information.
 */
data class SnackbarVisualsWithError(
    val isError: Boolean,
    override val message: String,
    override val actionLabel: String? = null,
    override val withDismissAction: Boolean = false,
    override val duration: SnackbarDuration = SnackbarDuration.Short
) : SnackbarVisuals

/**
 * Snackbar for authentication screens.
 */
@Composable
private fun AuthSnackbar(
    data: SnackbarData,
    visualsWithError: SnackbarVisualsWithError?
) {
    val containerColor: Color
    val contentColor: Color
    val actionColor: Color
    val actionContentColor: Color
    val dismissActionContentColor: Color

    if (visualsWithError?.isError == true) {
        containerColor = MaterialTheme.colorScheme.errorContainer
        contentColor = MaterialTheme.colorScheme.onErrorContainer
        actionColor = MaterialTheme.colorScheme.error
        actionContentColor = MaterialTheme.colorScheme.onError
        dismissActionContentColor = MaterialTheme.colorScheme.onErrorContainer
    } else {
        containerColor = MaterialTheme.colorScheme.inverseSurface
        contentColor = MaterialTheme.colorScheme.inverseOnSurface
        actionColor = MaterialTheme.colorScheme.inversePrimary
        actionContentColor = MaterialTheme.colorScheme.inverseOnSurface
        dismissActionContentColor = MaterialTheme.colorScheme.inverseOnSurface
    }

    Snackbar(
        snackbarData = data,
        modifier = Modifier.padding(12.dp),
        containerColor = containerColor,
        contentColor = contentColor,
        actionColor = actionColor,
        actionContentColor = actionContentColor,
        dismissActionContentColor = dismissActionContentColor
    )
}

/**
 * Snackbar for main application screens.
 */
@Composable
private fun MainAppSnackbar(
    data: SnackbarData,
    visualsWithError: SnackbarVisualsWithError?
) {
    // Same implementation as AuthSnackbar - could be extracted to shared component
    AuthSnackbar(data, visualsWithError)
}
```

## Key Implementation Features

### Authentication-First Architecture
- **State-Driven Navigation**: Routes based on `AuthState` from AuthRepository
- **Startup Check**: Validates existing tokens on app launch
- **Seamless Transitions**: Smooth flow between auth and main app

### Navigation Security
- **Authentication Guards**: Main app only accessible when authenticated
- **Back Stack Management**: Prevents navigation back to auth screens when authenticated
- **State Preservation**: Maintains navigation state within main app

### User Experience
- **Loading States**: Shows loading screen during auth state checking
- **User Context**: Displays username in top bar
- **Logout Functionality**: Easy logout access through user menu
- **Error Handling**: Global error handling with contextual snackbars

### Scalability
- **Modular Design**: Separate flows for auth and main app
- **Extensible Routes**: Easy to add new routes and screens
- **State Management**: Centralized auth state management

## Migration Notes

### Breaking Changes
This implementation requires a complete restructuring of the existing AppShell.kt:

1. **Remove direct navigation** to Chat screen on startup
2. **Add authentication state checking** as the first step
3. **Implement conditional navigation** based on auth state
4. **Add user menu and logout functionality**

### Backward Compatibility
- Existing ViewModels (SessionListViewModel, ChatViewModel) remain unchanged
- Main app screens (ChatScreen, SettingsScreen) remain unchanged
- Only the navigation structure changes

### Testing Impact
- Update navigation tests to account for authentication flow
- Add tests for authentication state transitions
- Test logout functionality and state cleanup

## Advanced Navigation Patterns

### Deep Linking Support
```kotlin
// Add to AppRoute.kt for future deep linking support
@Serializable
@SerialName("chat_session")
data class ChatSession(val sessionId: Long) : AppRoute {
    override val name = "chat_session"
    override val route = "chat_session/$sessionId"
}

// In MainApplicationFlow, add deep link handling
composable<ChatSession> { backStackEntry ->
    val sessionId = backStackEntry.arguments?.getLong("sessionId") ?: 0L
    // Handle deep link to specific chat session
    LaunchedEffect(sessionId) {
        if (sessionId > 0) {
            sessionListViewModel.selectSession(sessionId)
        }
    }
    ChatScreen(sessionListViewModel, chatViewModel)
}
```

### Navigation State Persistence
```kotlin
// Add to MainApplicationFlow for state persistence across app restarts
@Composable
private fun MainApplicationFlow(
    authState: AuthState.Authenticated,
    snackbarHostState: SnackbarHostState,
    authViewModel: AuthViewModel
) {
    val navController = rememberNavController()

    // Save navigation state
    DisposableEffect(navController) {
        val listener = NavController.OnDestinationChangedListener { _, destination, _ ->
            // Save current route for restoration
            // Implementation depends on your persistence strategy
        }
        navController.addOnDestinationChangedListener(listener)

        onDispose {
            navController.removeOnDestinationChangedListener(listener)
        }
    }

    // Rest of implementation...
}
```

### Error Recovery Navigation
```kotlin
// Add error recovery routes
@Serializable
@SerialName("auth_error")
data class AuthError(val message: String) : AppRoute {
    override val name = "auth_error"
    override val route = "auth_error"
}

// In AuthenticationFlow, add error handling
composable<AuthError> { backStackEntry ->
    val errorMessage = backStackEntry.arguments?.getString("message") ?: "Unknown error"
    AuthErrorScreen(
        message = errorMessage,
        onRetry = { authViewModel.checkInitialAuthState() },
        onGoToLogin = { navController.navigate(Login) }
    )
}
```

## Testing Strategies

### Navigation Testing
```kotlin
@Test
fun `authentication flow navigates correctly`() {
    // Test navigation between login and register screens
    val navController = TestNavHostController(context)

    composeTestRule.setContent {
        AuthenticationFlow(
            snackbarHostState = SnackbarHostState(),
            authViewModel = mockAuthViewModel
        )
    }

    // Verify initial route
    assertEquals(Login.route, navController.currentDestination?.route)

    // Test navigation to register
    composeTestRule.onNodeWithText("Sign Up").performClick()
    assertEquals(Register.route, navController.currentDestination?.route)
}
```

### Authentication State Testing
```kotlin
@Test
fun `app shell routes based on auth state`() {
    val authStateFlow = MutableStateFlow<AuthState>(AuthState.Loading)

    composeTestRule.setContent {
        AppShell()
    }

    // Test loading state
    composeTestRule.onNodeWithText("Checking authentication...").assertIsDisplayed()

    // Test unauthenticated state
    authStateFlow.value = AuthState.Unauthenticated
    composeTestRule.onNodeWithText("Welcome Back").assertIsDisplayed()

    // Test authenticated state
    authStateFlow.value = AuthState.Authenticated(1L, "testuser")
    composeTestRule.onNodeWithText("Chatbot - testuser").assertIsDisplayed()
}
```

## Performance Optimizations

### Lazy Loading
```kotlin
// Optimize ViewModel creation for authenticated flow
@Composable
private fun MainApplicationFlow(
    authState: AuthState.Authenticated,
    snackbarHostState: SnackbarHostState,
    authViewModel: AuthViewModel
) {
    // Only create ViewModels when needed
    val sessionListViewModel: SessionListViewModel by lazy { koinViewModel() }
    val chatViewModel: ChatViewModel by lazy { koinViewModel() }

    // Rest of implementation...
}
```

### Memory Management
```kotlin
// Add proper cleanup in navigation transitions
LaunchedEffect(authState) {
    if (authState is AuthState.Unauthenticated) {
        // Clear any cached data when user logs out
        // This prevents memory leaks and data exposure
    }
}
```

## Security Considerations

### Route Protection
```kotlin
// Add route-level security checks
@Composable
private fun ProtectedRoute(
    authState: AuthState,
    content: @Composable () -> Unit
) {
    when (authState) {
        is AuthState.Authenticated -> content()
        else -> {
            // Redirect to login if not authenticated
            LaunchedEffect(Unit) {
                // Handle unauthorized access
            }
        }
    }
}
```

### Session Validation
```kotlin
// Add periodic session validation in MainApplicationFlow
LaunchedEffect(authState) {
    if (authState is AuthState.Authenticated) {
        // Periodically validate session
        while (true) {
            delay(5.minutes)
            authViewModel.validateCurrentSession()
        }
    }
}
```

## Customization Options

### Theme-Aware Navigation
```kotlin
// Customize navigation based on theme
@Composable
private fun ThemedNavigationBar() {
    NavigationBar(
        containerColor = if (isSystemInDarkTheme()) {
            MaterialTheme.colorScheme.surface
        } else {
            MaterialTheme.colorScheme.primaryContainer
        }
    ) {
        // Navigation items...
    }
}
```

### Conditional Features
```kotlin
// Show different features based on user permissions
@Composable
private fun ConditionalNavigation(authState: AuthState.Authenticated) {
    if (authState.hasAdminPermissions) {
        NavigationBarItem(
            icon = { Icon(Icons.Default.AdminPanel, contentDescription = "Admin") },
            label = { Text("Admin") },
            selected = false,
            onClick = { /* Navigate to admin panel */ }
        )
    }
}
```

## Migration Checklist

### Pre-Migration
- [ ] Backup existing AppShell.kt
- [ ] Ensure AuthViewModel is fully implemented
- [ ] Verify authentication screens are complete
- [ ] Update navigation dependencies

### Migration Steps
1. [ ] Update AppRoute.kt with authentication routes
2. [ ] Replace AppShell.kt with new implementation
3. [ ] Add missing import statements
4. [ ] Update Koin module if needed
5. [ ] Test authentication flow
6. [ ] Test main app navigation
7. [ ] Verify logout functionality

### Post-Migration
- [ ] Update navigation tests
- [ ] Test deep linking (if applicable)
- [ ] Verify error handling
- [ ] Performance testing
- [ ] User acceptance testing

This implementation provides a complete, secure navigation system that properly handles authentication state and provides a seamless user experience.
