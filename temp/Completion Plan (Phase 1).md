# User Accounts Feature - Completion Plan (Phase 1)

## Overview
This plan details the implementation of the remaining components to complete Phase 1 of the user accounts feature. Based on the current status, we need to implement the authentication UI, ViewModels, and navigation integration to provide a complete user authentication experience.

## Task Breakdown

### Task 1: AuthViewModel Implementation
**Priority**: Critical (Blocks all UI work)
**Estimated Effort**: 2-3 days
**Dependencies**: None (uses existing AuthRepository)

#### 1.1 Create AuthViewModel
**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/auth/AuthViewModel.kt`

**Implementation Requirements:**
```kotlin
class AuthViewModel(
    private val authRepository: AuthRepository,
    private val errorNotifier: ErrorNotifier,
    private val normalScope: CoroutineScope
) : ViewModel(normalScope) {
    
    // Expose auth state from repository
    val authState: StateFlow<AuthState> = authRepository.authState
    
    // Form state management
    private val _loginFormState = MutableStateFlow(LoginFormState())
    val loginFormState: StateFlow<LoginFormState> = _loginFormState.asStateFlow()
    
    private val _registerFormState = MutableStateFlow(RegisterFormState())
    val registerFormState: StateFlow<RegisterFormState> = _registerFormState.asStateFlow()
    
    // Actions
    suspend fun login(username: String, password: String)
    suspend fun register(username: String, password: String, email: String?)
    suspend fun logout()
    fun updateLoginForm(username: String, password: String)
    fun updateRegisterForm(username: String, password: String, email: String?)
    fun clearForms()
    suspend fun checkInitialAuthState()
}
```

#### 1.2 Create Form State Classes
**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/auth/AuthFormState.kt`

**Implementation Requirements:**
- `LoginFormState`: username, password, validation errors, loading state
- `RegisterFormState`: username, password, email, validation errors, loading state
- Form validation logic for each field
- Error state management

**Acceptance Criteria:**
- [ ] AuthViewModel manages authentication operations
- [ ] Form state is properly validated
- [ ] Errors are handled and displayed appropriately
- [ ] Loading states are managed during async operations
- [ ] Integration with existing ErrorNotifier pattern

---

### Task 2: Authentication Screens Implementation
**Priority**: Critical
**Estimated Effort**: 3-4 days
**Dependencies**: Task 1 (AuthViewModel)

#### 2.1 Create LoginScreen
**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/auth/LoginScreen.kt`

**Implementation Requirements:**
```kotlin
@Composable
fun LoginScreen(
    onNavigateToRegister: () -> Unit,
    authViewModel: AuthViewModel = koinViewModel()
) {
    // Form fields: username/email, password
    // Login button with loading state
    // "Register" navigation link
    // Error display
    // Form validation feedback
}
```

**UI Components Needed:**
- Username/email input field with validation
- Password input field with visibility toggle
- Login button with loading indicator
- "Don't have an account? Register" link
- Error message display
- Form validation indicators

#### 2.2 Create RegisterScreen
**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/auth/RegisterScreen.kt`

**Implementation Requirements:**
```kotlin
@Composable
fun RegisterScreen(
    onNavigateToLogin: () -> Unit,
    onRegistrationSuccess: () -> Unit,
    authViewModel: AuthViewModel = koinViewModel()
) {
    // Form fields: username, email (optional), password, confirm password
    // Register button with loading state
    // "Login" navigation link
    // Error display
    // Form validation feedback
}
```

**UI Components Needed:**
- Username input with availability checking
- Email input (optional) with validation
- Password input with strength indicator
- Confirm password input with matching validation
- Register button with loading indicator
- "Already have an account? Login" link
- Terms of service acceptance (if needed)

#### 2.3 Create Supporting Screens
**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/auth/AuthLoadingScreen.kt`
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/auth/AuthErrorScreen.kt`

**AuthLoadingScreen Requirements:**
- Splash screen with app logo
- Loading indicator
- "Checking authentication..." message

**AuthErrorScreen Requirements:**
- Error message display
- Retry button
- Option to go to login screen

**Acceptance Criteria:**
- [ ] LoginScreen handles user input and validation
- [ ] RegisterScreen handles registration flow
- [ ] Both screens integrate with AuthViewModel
- [ ] Loading and error states are properly displayed
- [ ] Navigation between screens works correctly
- [ ] Forms follow Material Design guidelines

---

### Task 3: Navigation Integration
**Priority**: Critical
**Estimated Effort**: 2 days
**Dependencies**: Task 2 (Authentication Screens)

#### 3.1 Update AppRoute
**Files to Modify:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/navigation/AppRoute.kt`

**Changes Required:**
```kotlin
// Add new authentication routes
@Serializable
@SerialName("login")
object Login : AppRoute {
    override val name = "login"
    override val route = "login"
}

@Serializable
@SerialName("register")
object Register : AppRoute {
    override val name = "register"
    override val route = "register"
}

@Serializable
@SerialName("auth_loading")
object AuthLoading : AppRoute {
    override val name = "auth_loading"
    override val route = "auth_loading"
}
```

#### 3.2 Update AppShell with Authentication Flow
**Files to Modify:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/AppShell.kt`

**Major Changes Required:**
```kotlin
@Composable
fun AppShell() {
    val authViewModel: AuthViewModel = koinViewModel()
    val authState by authViewModel.authState.collectAsState()
    
    // Check initial auth state on startup
    LaunchedEffect(Unit) {
        authViewModel.checkInitialAuthState()
    }
    
    when (authState) {
        is AuthState.Loading -> AuthLoadingScreen()
        is AuthState.Unauthenticated -> AuthenticationFlow()
        is AuthState.Authenticated -> MainApplicationFlow(authState)
    }
}

@Composable
private fun AuthenticationFlow() {
    val navController = rememberNavController()
    NavHost(navController = navController, startDestination = Login) {
        composable<Login> { 
            LoginScreen(
                onNavigateToRegister = { navController.navigate(Register) }
            )
        }
        composable<Register> { 
            RegisterScreen(
                onNavigateToLogin = { navController.navigate(Login) },
                onRegistrationSuccess = { navController.navigate(Login) }
            )
        }
    }
}

@Composable
private fun MainApplicationFlow(authState: AuthState.Authenticated) {
    // Existing main app navigation with user context
    // Add logout functionality to top bar
}
```

**Acceptance Criteria:**
- [ ] App checks authentication state on startup
- [ ] Unauthenticated users see login screen
- [ ] Authenticated users see main app
- [ ] Navigation between auth screens works
- [ ] Main app is protected from unauthenticated access

---

### Task 4: Startup Authentication Check
**Priority**: High
**Estimated Effort**: 1 day
**Dependencies**: Task 1 (AuthViewModel)

#### 4.1 Implement Initial Auth State Check
**Files to Modify:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/auth/AuthViewModel.kt`
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/impl/DefaultAuthRepository.kt`

**Implementation Requirements:**
```kotlin
// In AuthViewModel
suspend fun checkInitialAuthState() {
    authRepository.checkInitialAuthState()
}

// In DefaultAuthRepository
suspend fun checkInitialAuthState() {
    _authState.value = AuthState.Loading
    
    // Check if we have valid tokens
    val accessToken = tokenStorage.getAccessToken()
    if (accessToken.isRight()) {
        // Validate token with server by calling /auth/me
        authApi.getCurrentUser().fold(
            ifLeft = { 
                // Token invalid, clear and set unauthenticated
                tokenStorage.clearTokens()
                _authState.value = AuthState.Unauthenticated
            },
            ifRight = { user ->
                // Token valid, set authenticated state
                _authState.value = AuthState.Authenticated(user.id, user.username)
            }
        )
    } else {
        _authState.value = AuthState.Unauthenticated
    }
}
```

**Acceptance Criteria:**
- [ ] App validates existing tokens on startup
- [ ] Invalid tokens are cleared automatically
- [ ] Valid tokens restore authenticated state
- [ ] Loading state is shown during validation

---

### Task 5: User Feedback and Polish
**Priority**: Medium
**Estimated Effort**: 1-2 days
**Dependencies**: Tasks 1-4

#### 5.1 Enhanced Error Handling
**Improvements Needed:**
- Better error messages for common scenarios
- Network connectivity error handling
- Form validation with real-time feedback
- Success messages for registration

#### 5.2 User Experience Enhancements
**Improvements Needed:**
- Remember username on login screen
- Auto-focus on form fields
- Keyboard navigation support
- Loading indicators during API calls
- Smooth transitions between screens

#### 5.3 Logout Functionality
**Files to Modify:**
- Main app top bar (add user menu with logout)
- AuthViewModel (logout action)

**Acceptance Criteria:**
- [ ] Users can logout from main app
- [ ] Logout clears tokens and returns to login
- [ ] Error messages are user-friendly
- [ ] Loading states provide good UX

---

## Implementation Sequence

### Week 1 (Days 1-3)
1. **Day 1**: Implement AuthViewModel and form state classes
2. **Day 2**: Create LoginScreen with basic functionality
3. **Day 3**: Create RegisterScreen and supporting screens

### Week 2 (Days 4-7)
4. **Day 4**: Update navigation routes and AppShell integration
5. **Day 5**: Implement startup authentication check
6. **Day 6**: Add logout functionality and user feedback
7. **Day 7**: Testing, polish, and bug fixes

## Testing Strategy

### Unit Tests
- AuthViewModel form validation logic
- Authentication state transitions
- Token validation logic

### Integration Tests
- Login/register flow end-to-end
- Token refresh during authenticated sessions
- Logout and state cleanup

### UI Tests
- Form validation feedback
- Navigation between auth screens
- Loading and error states

## Risk Mitigation

### Medium Risks
1. **State Synchronization**: Ensure auth state is consistent across components
   - *Mitigation*: Use single source of truth (AuthRepository)

2. **Token Refresh During Auth Flow**: Handle edge cases during login
   - *Mitigation*: Clear tokens before login attempt

3. **Navigation State**: Prevent back navigation to auth screens when authenticated
   - *Mitigation*: Use proper navigation flags and state management

### Success Criteria
- [ ] Users can register new accounts
- [ ] Users can log in with credentials
- [ ] App remembers authentication state
- [ ] Token refresh works transparently
- [ ] Users can logout successfully
- [ ] All error cases are handled gracefully
- [ ] UI follows Material Design guidelines
- [ ] Performance is acceptable on all platforms

## Estimated Total Effort: 9-12 days

This plan will complete Phase 1 of the user accounts feature, providing a fully functional authentication system with a polished user experience.
