# Phase 1: Authentication Infrastructure - Development Plan (Revision 1)
## Overview
This plan details the implementation of Phase 1 of the user accounts feature, focusing on building the core authentication infrastructure for the Kotlin/Compose chatbot application.

**This revision makes a significant architectural change:** Instead of building a custom `AuthenticatedHttpClient` wrapper, this plan adopts the standard, robust **`ktor-client-auth` plugin**. This idiomatic Ktor approach provides transparent token injection, automatic refresh, and request retries, integrating cleanly with our `Either`-based `TokenStorage` and `AuthApi` components. This simplifies development and reduces maintenance overhead.

## Task Breakdown
### Task 1: Token Management Infrastructure
#### 1.1 Platform-Agnostic Token Storage Interface
**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/TokenStorage.kt`
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/TokenStorageError.kt`

**Interface Definition:**
```kotlin
// file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/auth/TokenStorage.kt
package eu.torvian.chatbot.app.service.auth

import arrow.core.Either
import kotlinx.datetime.Instant

interface TokenStorage {
    suspend fun saveTokens(accessToken: String, refreshToken: String, expiresAt: Instant): Either<TokenStorageError, Unit>
    suspend fun getAccessToken(): Either<TokenStorageError, String>
    suspend fun getRefreshToken(): Either<TokenStorageError, String>
    suspend fun getExpiry(): Either<TokenStorageError, Instant>
    suspend fun clearTokens(): Either<TokenStorageError, Unit>
    suspend fun isTokenExpired(token: String): Either<TokenStorageError, Boolean>
}
```

**Deliverables:**
- `TokenStorage` interface and `TokenStorageError` sealed class with comprehensive KDoc.
- A clear contract for platform-specific implementations to handle errors using `Either`.

**Acceptance Criteria:**
- [ ] Interface and error types compile and follow existing codebase patterns.
- [ ] All methods return `Either<TokenStorageError, T>`.

#### 1.2 Desktop Token Storage Implementation
**Files to Create:**
- `app/src/desktopMain/kotlin/eu/torvian/chatbot/app/service/auth/DesktopTokenStorage.kt`

**Platform Considerations:**
- Use file-based storage in user home directory (`.chatbot/tokens.enc`).
- Implement encryption for token security.
- Map all file I/O and encryption exceptions to `TokenStorageError` and return as `Either.Left`.

**Acceptance Criteria:**
- [ ] Tokens are encrypted before storage.
- [ ] File and encryption failures are returned as `TokenStorageError.Left`.
- [ ] Unit tests cover both success (`Right`) and failure (`Left`) scenarios.

#### 1.3 Web Token Storage Implementation
**Files to Create:**
- `app/src/wasmJsMain/kotlin/eu/torvian/chatbot/app/service/auth/WebTokenStorage.kt`

**Platform Considerations:**
- Use browser `localStorage` with client-side encryption.
- Map `localStorage` quota limits and other exceptions to `TokenStorageError`.

**Acceptance Criteria:**
- [ ] Tokens are encrypted before being stored in `localStorage`.
- [ ] Storage quota limits are handled and returned as `TokenStorageError.Left`.
- [ ] Unit tests using MockK for `localStorage` cover `Right` and `Left` paths.

**Estimated Effort:** 2-3 days
**Dependencies:** None
**Risks:** Platform-specific encryption complexity.

---
### Task 2: Ktor Auth Plugin Configuration
#### 2.1 Configure Ktor `Auth` Plugin with Bearer Provider
**Files to Modify:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/koin/appModule.kt`

**Implementation:**
The core of this task is to configure two `HttpClient` instances in Koin. The authenticated client will have the `Auth` plugin installed and configured to use our `TokenStorage` and `AuthApi`.

```kotlin
// In appModule.kt
// ...
install(Auth) {
    bearer {
        loadTokens {
            // Bridge our Either-based storage to the plugin's nullable BearerTokens
            val result = either<Any, BearerTokens> {
                val accessToken = tokenStorage.getAccessToken().bind()
                val refreshToken = tokenStorage.getRefreshToken().bind()
                BearerTokens(accessToken, refreshToken)
            }
            result.getOrNull()
        }

        refreshTokens {
            val oldRefreshToken = oldTokens?.refreshToken ?: run {
                authRepository.logout() // No refresh token, so we must be logged out.
                return@refreshTokens null
            }

            // Use AuthApi (with unauthenticated client) to refresh
            val refreshResult = authApi.refreshToken(RefreshTokenRequest(oldRefreshToken))
            
            refreshResult.fold(
                ifLeft = {
                    authRepository.logout() // Refresh was rejected by the server.
                    null // Return null to signify refresh failure.
                },
                ifRight = { loginResponse ->
                    tokenStorage.saveTokens(
                        loginResponse.accessToken,
                        loginResponse.refreshToken,
                        loginResponse.expiresAt
                    )
                    BearerTokens(loginResponse.accessToken, loginResponse.refreshToken)
                }
            )
        }
        
        sendWithoutRequest { request ->
            !request.url.encodedPath.contains("/api/v1/auth/")
        }
    }
}
// ...
```

**Key Features:**
- Uses the standard, well-tested `ktor-client-auth` plugin.
- Provides transparent token injection into requests.
- Handles `401 Unauthorized` responses by automatically triggering the `refreshTokens` logic.
- Retries the original request automatically upon successful token refresh.
- **Requires no changes to existing API client classes** (`KtorGroupApiClient`, etc.).

**Acceptance Criteria:**
- [ ] An authenticated `HttpClient` is configured in Koin with the `Auth` plugin.
- [ ] `loadTokens` lambda correctly retrieves tokens from `TokenStorage`.
- [ ] `refreshTokens` lambda correctly uses `AuthApi` and updates `TokenStorage`.
- [ ] `401` responses trigger the refresh flow, and the original request is retried on success.
- [ ] A global logout is triggered via `AuthRepository` if token refresh fails.

**Estimated Effort:** 2-3 days
**Dependencies:** Task 1 (TokenStorage), Task 3 (AuthApi)
**Risks:** Correctly configuring the plugin's suspendable lambdas with our `Either`-based services.

---
### Task 3: Authentication API Client
#### 3.1 Authentication API Client Implementation
**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/AuthApi.kt` (interface)
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorAuthApiClient.kt`

**Implementation:**
- Follows existing API client patterns (`BaseApiResourceClient`).
- **Crucially, this client must be injected with the *unauthenticated* `HttpClient`** to prevent an infinite loop where a failed refresh attempt tries to refresh itself.

**Acceptance Criteria:**
- [ ] All authentication endpoints are implemented.
- [ ] Uses `safeApiCall` for error handling.
- [ ] Injected with the correct (unauthenticated) `HttpClient` via Koin.

**Estimated Effort:** 2 days
**Dependencies:** None
**Risks:** Low - follows established patterns.

---
### Task 4: Authentication Repository
#### 4.1 Authentication Repository Implementation
**Files to Create:**
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/AuthRepository.kt` (interface)
- `app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/impl/DefaultAuthRepository.kt`

**Implementation Features:**
- Manages reactive authentication state (`StateFlow<AuthState>`).
- The `logout()` method will be called from the Ktor `Auth` plugin's `refreshTokens` lambda on catastrophic failure, centralizing the logout logic.
- Integrates with `TokenStorage` and `AuthApi` using `Either`.

**Acceptance Criteria:**
- [ ] `login` and `register` operations correctly save tokens via `TokenStorage`.
- [ ] `logout` correctly clears tokens and updates `authState`.
- [ ] `authState` accurately reflects the user's authentication status.

**Estimated Effort:** 3 days
**Dependencies:** Task 1 (TokenStorage), Task 3 (AuthApi)
**Risks:** State management complexity.

---
## Implementation Order & Dependencies
```mermaid
graph TD
    A[Task 1: TokenStorage] --> D[Task 2: Configure Ktor Auth Plugin]
    C[Task 3: AuthApi Client] --> D
    D --> E[Task 4: AuthRepository]
    A --> E
    C --> E
    E --> F[Integration & Testing]
```
**Recommended Implementation Sequence:**
1.  **Task 1 & 3 in parallel:** Implement `TokenStorage` and `AuthApi` as they are independent prerequisites.
2.  **Task 2:** Configure the Ktor `Auth` plugin in Koin, using the components from Task 1 & 3.
3.  **Task 4:** Implement the `AuthRepository`, which will also be used by the plugin configuration from Task 2.
4.  **Final Integration & Testing:** Wire up all components and perform end-to-end tests.

---
## Testing Strategy
### Unit Tests
**Token Storage Tests:**
- Verify that `saveTokens`, `getAccessToken`, etc., return `Either.Right` on success and `Either.Left` with the correct `TokenStorageError` on failure (e.g., IO, encryption).

**Repository Tests:**
- Mock `AuthApi` and `TokenStorage` to return `Left` and `Right` values and assert that `DefaultAuthRepository` handles these outcomes correctly and updates its `authState`.

### Integration Tests
**Ktor Auth Plugin Flow:**
- Create a test `HttpClient` with the `Auth` plugin installed.
- Use a `MockEngine` to simulate backend responses.
- **Test Case 1 (Happy Path):** Verify the `Authorization` header is added.
- **Test Case 2 (Refresh Flow):**
    1.  Have the `MockEngine` return a `401 Unauthorized` on the first request.
    2.  Verify the `refreshTokens` lambda is called.
    3.  Have the `MockEngine` return a successful token response for the refresh call.
    4.  Verify the original request is retried with the new token and ultimately succeeds.
- **Test Case 3 (Refresh Failure):**
    1.  Have the `MockEngine` return a `401` on the first request.
    2.  Have the `MockEngine` also return an error for the refresh call.
    3.  Verify that `AuthRepository.logout()` is called and the final result is a `401` error.

---
## Integration Points
### Koin Dependency Injection Updates
**Module Changes:**
```kotlin
// file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/koin/appModule.kt

// Define Koin qualifiers for clarity
val UnauthenticatedClient = named("UnauthenticatedClient")
val AuthenticatedClient = named("AuthenticatedClient")

fun appModule(baseUri: String): Module = module {
    // ...
    
    // Provide TokenStorage, AuthApi, and AuthRepository
    single<TokenStorage> { /* ... */ }
    single<AuthApi> { KtorAuthApiClient(get(UnauthenticatedClient)) }
    single<AuthRepository> { DefaultAuthRepository(get(), get(), get()) }
    
    // Provide the two HttpClient instances
    single(UnauthenticatedClient) { createHttpClient(baseUri, Json) }
    single(AuthenticatedClient) {
        val unauthenticatedClient = get<HttpClient>(UnauthenticatedClient)
        // ... configure client with Auth plugin as shown in Task 2 ...
    }

    // Inject authenticated client into data APIs
    single<GroupApi> { KtorGroupApiClient(get(AuthenticatedClient)) }
    single<SessionApi> { KtorSessionApiClient(get(AuthenticatedClient)) }
    // ...
}
```

---
## Estimated Effort & Risks
### Total Estimated Effort: 10-12 days
**Task Breakdown:**
- Task 1 (Token Management): 2-3 days
- Task 2 (Ktor Auth Plugin Config): 2-3 days
- Task 3 (Authentication API Client): 2 days
- Task 4 (Authentication Repository): 3 days
- Integration & Testing: 1-2 days

### Potential Risks & Mitigation
**Medium Risk:**
- **Correctly configuring `refreshTokens` lambda:** This is the most complex part, involving bridging our `Either`-based services with the plugin's callback logic.
    - *Mitigation:* Write thorough integration tests with a `MockEngine` to validate the entire refresh and retry flow before integrating with the UI.

**Low Risk:**
- **Platform-specific encryption:** (Mitigated by using established libraries).
- **API client implementation:** (Follows established patterns).

### Success Metrics
- [ ] All unit and integration tests passing.
- [ ] The application transparently refreshes tokens on `401` responses without interrupting the user.
- [ ] The application logs the user out if a token refresh fails permanently.
- [ ] No breaking changes to existing API client or repository method signatures.

This plan provides a solid, idiomatic foundation for implementing authentication infrastructure using standard Ktor features, ensuring maintainability and robustness.
