# Reference Implementation: Task 1 - AuthViewModel Implementation

## Overview
This document provides a complete reference implementation for the AuthViewModel and related form state classes. The implementation follows existing codebase patterns and integrates seamlessly with the current architecture.

## File Structure
```
app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/auth/
├── AuthViewModel.kt
├── AuthFormState.kt
└── AuthFormValidation.kt
```

## Implementation

### 1. AuthFormState.kt
```kotlin
package eu.torvian.chatbot.app.viewmodel.auth

import kotlinx.serialization.Serializable

/**
 * Represents the state of the login form.
 */
@Serializable
data class LoginFormState(
    val username: String = "",
    val password: String = "",
    val isLoading: Boolean = false,
    val usernameError: String? = null,
    val passwordError: String? = null,
    val generalError: String? = null
) {
    val isValid: Boolean
        get() = username.isNotBlank() && 
                password.isNotBlank() && 
                usernameError == null && 
                passwordError == null

    val hasErrors: Boolean
        get() = usernameError != null || passwordError != null || generalError != null
}

/**
 * Represents the state of the registration form.
 */
@Serializable
data class RegisterFormState(
    val username: String = "",
    val email: String = "",
    val password: String = "",
    val confirmPassword: String = "",
    val isLoading: Boolean = false,
    val usernameError: String? = null,
    val emailError: String? = null,
    val passwordError: String? = null,
    val confirmPasswordError: String? = null,
    val generalError: String? = null
) {
    val isValid: Boolean
        get() = username.isNotBlank() && 
                password.isNotBlank() && 
                password == confirmPassword &&
                usernameError == null && 
                emailError == null && 
                passwordError == null && 
                confirmPasswordError == null

    val hasErrors: Boolean
        get() = usernameError != null || 
                emailError != null || 
                passwordError != null || 
                confirmPasswordError != null || 
                generalError != null
}
```

### 2. AuthFormValidation.kt
```kotlin
package eu.torvian.chatbot.app.viewmodel.auth

/**
 * Validation utilities for authentication forms.
 */
object AuthFormValidation {
    
    private const val MIN_USERNAME_LENGTH = 3
    private const val MAX_USERNAME_LENGTH = 50
    private const val MIN_PASSWORD_LENGTH = 8
    
    private val EMAIL_REGEX = Regex(
        "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
    )
    
    private val USERNAME_REGEX = Regex("^[a-zA-Z0-9_-]+$")

    /**
     * Validates a username field.
     */
    fun validateUsername(username: String): String? {
        return when {
            username.isBlank() -> "Username is required"
            username.length < MIN_USERNAME_LENGTH -> 
                "Username must be at least $MIN_USERNAME_LENGTH characters"
            username.length > MAX_USERNAME_LENGTH -> 
                "Username must be no more than $MAX_USERNAME_LENGTH characters"
            !USERNAME_REGEX.matches(username) -> 
                "Username can only contain letters, numbers, hyphens, and underscores"
            else -> null
        }
    }

    /**
     * Validates an email field (optional field).
     */
    fun validateEmail(email: String): String? {
        return when {
            email.isBlank() -> null // Email is optional
            !EMAIL_REGEX.matches(email) -> "Please enter a valid email address"
            else -> null
        }
    }

    /**
     * Validates a password field.
     */
    fun validatePassword(password: String): String? {
        return when {
            password.isBlank() -> "Password is required"
            password.length < MIN_PASSWORD_LENGTH -> 
                "Password must be at least $MIN_PASSWORD_LENGTH characters"
            !password.any { it.isDigit() } -> 
                "Password must contain at least one number"
            !password.any { it.isLetter() } -> 
                "Password must contain at least one letter"
            else -> null
        }
    }

    /**
     * Validates password confirmation.
     */
    fun validateConfirmPassword(password: String, confirmPassword: String): String? {
        return when {
            confirmPassword.isBlank() -> "Please confirm your password"
            password != confirmPassword -> "Passwords do not match"
            else -> null
        }
    }
}
```

### 3. AuthViewModel.kt
```kotlin
package eu.torvian.chatbot.app.viewmodel.auth

import androidx.lifecycle.ViewModel
import arrow.core.raise.either
import eu.torvian.chatbot.app.repository.AuthRepository
import eu.torvian.chatbot.app.repository.AuthState
import eu.torvian.chatbot.app.repository.RepositoryError
import eu.torvian.chatbot.app.viewmodel.common.ErrorNotifier
import eu.torvian.chatbot.app.utils.misc.kmpLogger
import eu.torvian.chatbot.common.models.auth.LoginRequest
import eu.torvian.chatbot.common.models.auth.RegisterRequest
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel for managing authentication UI state and operations.
 *
 * This ViewModel handles:
 * - Authentication state management (delegated to AuthRepository)
 * - Login and registration form state
 * - Form validation and error handling
 * - User authentication operations
 *
 * @param authRepository Repository for authentication operations
 * @param errorNotifier Service for handling and notifying about errors
 * @param normalScope Coroutine scope for normal operations
 */
class AuthViewModel(
    private val authRepository: AuthRepository,
    private val errorNotifier: ErrorNotifier,
    private val normalScope: CoroutineScope
) : ViewModel(normalScope) {

    companion object {
        private val logger = kmpLogger<AuthViewModel>()
    }

    // --- Authentication State (delegated to repository) ---

    /**
     * The current authentication state from the repository.
     */
    val authState: StateFlow<AuthState> = authRepository.authState

    // --- Form State Management ---

    private val _loginFormState = MutableStateFlow(LoginFormState())
    val loginFormState: StateFlow<LoginFormState> = _loginFormState.asStateFlow()

    private val _registerFormState = MutableStateFlow(RegisterFormState())
    val registerFormState: StateFlow<RegisterFormState> = _registerFormState.asStateFlow()

    // --- Authentication Operations ---

    /**
     * Attempts to log in the user with the provided credentials.
     */
    fun login(username: String, password: String) {
        viewModelScope.launch {
            logger.info("Attempting login for user: $username")
            
            // Validate form before submission
            val usernameError = AuthFormValidation.validateUsername(username)
            val passwordError = if (password.isBlank()) "Password is required" else null
            
            if (usernameError != null || passwordError != null) {
                _loginFormState.value = _loginFormState.value.copy(
                    usernameError = usernameError,
                    passwordError = passwordError,
                    generalError = null
                )
                return@launch
            }

            // Clear errors and set loading state
            _loginFormState.value = _loginFormState.value.copy(
                isLoading = true,
                usernameError = null,
                passwordError = null,
                generalError = null
            )

            // Perform login
            val result = authRepository.login(LoginRequest(username, password))
            
            result.fold(
                ifLeft = { error ->
                    logger.warn("Login failed for user $username: ${error.message}")
                    _loginFormState.value = _loginFormState.value.copy(
                        isLoading = false,
                        generalError = mapLoginError(error)
                    )
                },
                ifRight = {
                    logger.info("Login successful for user: $username")
                    _loginFormState.value = _loginFormState.value.copy(
                        isLoading = false,
                        generalError = null
                    )
                    // Clear form on successful login
                    clearLoginForm()
                }
            )
        }
    }

    /**
     * Attempts to register a new user with the provided information.
     */
    fun register(username: String, email: String, password: String, confirmPassword: String) {
        viewModelScope.launch {
            logger.info("Attempting registration for user: $username")
            
            // Validate all form fields
            val usernameError = AuthFormValidation.validateUsername(username)
            val emailError = AuthFormValidation.validateEmail(email)
            val passwordError = AuthFormValidation.validatePassword(password)
            val confirmPasswordError = AuthFormValidation.validateConfirmPassword(password, confirmPassword)
            
            if (usernameError != null || emailError != null || passwordError != null || confirmPasswordError != null) {
                _registerFormState.value = _registerFormState.value.copy(
                    usernameError = usernameError,
                    emailError = emailError,
                    passwordError = passwordError,
                    confirmPasswordError = confirmPasswordError,
                    generalError = null
                )
                return@launch
            }

            // Clear errors and set loading state
            _registerFormState.value = _registerFormState.value.copy(
                isLoading = true,
                usernameError = null,
                emailError = null,
                passwordError = null,
                confirmPasswordError = null,
                generalError = null
            )

            // Perform registration
            val emailToSend = if (email.isBlank()) null else email
            val result = authRepository.register(RegisterRequest(username, password, emailToSend))
            
            result.fold(
                ifLeft = { error ->
                    logger.warn("Registration failed for user $username: ${error.message}")
                    _registerFormState.value = _registerFormState.value.copy(
                        isLoading = false,
                        generalError = mapRegistrationError(error)
                    )
                },
                ifRight = { user ->
                    logger.info("Registration successful for user: ${user.username}")
                    _registerFormState.value = _registerFormState.value.copy(
                        isLoading = false,
                        generalError = null
                    )
                    // Clear form on successful registration
                    clearRegisterForm()
                    
                    // Show success message
                    errorNotifier.showSuccess("Account created successfully! Please log in.")
                }
            )
        }
    }

    /**
     * Logs out the current user.
     */
    fun logout() {
        viewModelScope.launch {
            logger.info("Attempting logout")
            
            val result = authRepository.logout()
            result.fold(
                ifLeft = { error ->
                    logger.warn("Logout failed: ${error.message}")
                    errorNotifier.repositoryError(
                        error = error,
                        shortMessage = "Logout failed"
                    )
                },
                ifRight = {
                    logger.info("Logout successful")
                    clearAllForms()
                }
            )
        }
    }

    /**
     * Checks the initial authentication state on app startup.
     */
    fun checkInitialAuthState() {
        viewModelScope.launch {
            logger.info("Checking initial authentication state")
            authRepository.checkInitialAuthState()
        }
    }

    // --- Form State Updates ---

    /**
     * Updates the login form state with new field values.
     */
    fun updateLoginForm(username: String, password: String) {
        _loginFormState.value = _loginFormState.value.copy(
            username = username,
            password = password,
            // Clear field-specific errors when user types
            usernameError = if (username != _loginFormState.value.username) null else _loginFormState.value.usernameError,
            passwordError = if (password != _loginFormState.value.password) null else _loginFormState.value.passwordError
        )
    }

    /**
     * Updates the registration form state with new field values.
     */
    fun updateRegisterForm(username: String, email: String, password: String, confirmPassword: String) {
        _registerFormState.value = _registerFormState.value.copy(
            username = username,
            email = email,
            password = password,
            confirmPassword = confirmPassword,
            // Clear field-specific errors when user types
            usernameError = if (username != _registerFormState.value.username) null else _registerFormState.value.usernameError,
            emailError = if (email != _registerFormState.value.email) null else _registerFormState.value.emailError,
            passwordError = if (password != _registerFormState.value.password) null else _registerFormState.value.passwordError,
            confirmPasswordError = if (confirmPassword != _registerFormState.value.confirmPassword) null else _registerFormState.value.confirmPasswordError
        )
    }

    /**
     * Clears the login form state.
     */
    fun clearLoginForm() {
        _loginFormState.value = LoginFormState()
    }

    /**
     * Clears the registration form state.
     */
    fun clearRegisterForm() {
        _registerFormState.value = RegisterFormState()
    }

    /**
     * Clears all form states.
     */
    fun clearAllForms() {
        clearLoginForm()
        clearRegisterForm()
    }

    // --- Error Mapping ---

    private fun mapLoginError(error: RepositoryError): String {
        return when (error) {
            is RepositoryError.ApiError -> when {
                error.message.contains("Invalid credentials", ignoreCase = true) -> 
                    "Invalid username or password"
                error.message.contains("User not found", ignoreCase = true) -> 
                    "Invalid username or password"
                error.message.contains("Account locked", ignoreCase = true) -> 
                    "Account is temporarily locked. Please try again later."
                else -> "Login failed. Please try again."
            }
            is RepositoryError.NetworkError -> 
                "Network error. Please check your connection and try again."
            is RepositoryError.OtherError -> 
                "An unexpected error occurred. Please try again."
        }
    }

    private fun mapRegistrationError(error: RepositoryError): String {
        return when (error) {
            is RepositoryError.ApiError -> when {
                error.message.contains("Username already exists", ignoreCase = true) -> 
                    "Username is already taken. Please choose a different one."
                error.message.contains("Email already exists", ignoreCase = true) -> 
                    "Email is already registered. Please use a different email or try logging in."
                else -> "Registration failed. Please try again."
            }
            is RepositoryError.NetworkError -> 
                "Network error. Please check your connection and try again."
            is RepositoryError.OtherError -> 
                "An unexpected error occurred. Please try again."
        }
    }
}
```

## Integration with Koin

### Update appModule.kt
```kotlin
// Add to app/src/commonMain/kotlin/eu/torvian/chatbot/app/koin/appModule.kt

// ViewModels section
viewModel { AuthViewModel(get(), get(), get()) }
```

## Usage Examples

### In Compose UI
```kotlin
@Composable
fun LoginScreen() {
    val authViewModel: AuthViewModel = koinViewModel()
    val loginFormState by authViewModel.loginFormState.collectAsState()
    val authState by authViewModel.authState.collectAsState()
    
    // Handle form submission
    val onLoginClick = {
        authViewModel.login(
            username = loginFormState.username,
            password = loginFormState.password
        )
    }
    
    // Handle form updates
    val onUsernameChange = { username: String ->
        authViewModel.updateLoginForm(
            username = username,
            password = loginFormState.password
        )
    }
    
    // UI implementation...
}
```

## Testing Considerations

### Unit Tests
- Test form validation logic
- Test error mapping functions
- Test state transitions
- Mock AuthRepository for isolated testing

### Integration Tests
- Test complete login/register flows
- Test error handling scenarios
- Test form state management

## Key Design Decisions

1. **Form State Separation**: Login and register forms have separate state classes for clarity
2. **Real-time Validation**: Errors are cleared when users modify fields
3. **Error Mapping**: Repository errors are mapped to user-friendly messages
4. **Loading States**: Each form manages its own loading state
5. **Integration**: Follows existing ViewModel patterns in the codebase

This implementation provides a solid foundation for the authentication UI while maintaining consistency with the existing codebase architecture.
