# Reference Implementation: Task 2 - Authentication Screens Implementation

## Overview
This document provides complete reference implementations for all authentication screens using Jetpack Compose and Material 3. The implementations integrate seamlessly with the AuthViewModel from Task 1 and follow the existing codebase UI patterns.

## File Structure
```
app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/auth/
├── LoginScreen.kt
├── RegisterScreen.kt
├── AuthLoadingScreen.kt
├── AuthErrorScreen.kt
└── AuthComponents.kt (shared components)
```

## Implementation

### 1. AuthComponents.kt (Shared Components)
```kotlin
package eu.torvian.chatbot.app.compose.auth

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp

/**
 * Reusable text field component for authentication forms.
 */
@Composable
fun AuthTextField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    modifier: Modifier = Modifier,
    isError: Boolean = false,
    errorMessage: String? = null,
    keyboardType: KeyboardType = KeyboardType.Text,
    imeAction: ImeAction = ImeAction.Next,
    onImeAction: () -> Unit = {},
    enabled: Boolean = true,
    singleLine: Boolean = true
) {
    val focusManager = LocalFocusManager.current
    
    Column(modifier = modifier) {
        OutlinedTextField(
            value = value,
            onValueChange = onValueChange,
            label = { Text(label) },
            isError = isError,
            enabled = enabled,
            singleLine = singleLine,
            keyboardOptions = KeyboardOptions(
                keyboardType = keyboardType,
                imeAction = imeAction
            ),
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) },
                onDone = { onImeAction() }
            ),
            modifier = Modifier.fillMaxWidth()
        )
        
        if (isError && errorMessage != null) {
            Text(
                text = errorMessage,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(start = 16.dp, top = 4.dp)
            )
        }
    }
}

/**
 * Password text field with visibility toggle.
 */
@Composable
fun PasswordTextField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    modifier: Modifier = Modifier,
    isError: Boolean = false,
    errorMessage: String? = null,
    imeAction: ImeAction = ImeAction.Next,
    onImeAction: () -> Unit = {},
    enabled: Boolean = true
) {
    var passwordVisible by remember { mutableStateOf(false) }
    val focusManager = LocalFocusManager.current
    
    Column(modifier = modifier) {
        OutlinedTextField(
            value = value,
            onValueChange = onValueChange,
            label = { Text(label) },
            isError = isError,
            enabled = enabled,
            singleLine = true,
            visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Password,
                imeAction = imeAction
            ),
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) },
                onDone = { onImeAction() }
            ),
            trailingIcon = {
                IconButton(onClick = { passwordVisible = !passwordVisible }) {
                    Icon(
                        imageVector = if (passwordVisible) Icons.Default.Visibility else Icons.Default.VisibilityOff,
                        contentDescription = if (passwordVisible) "Hide password" else "Show password"
                    )
                }
            },
            modifier = Modifier.fillMaxWidth()
        )
        
        if (isError && errorMessage != null) {
            Text(
                text = errorMessage,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(start = 16.dp, top = 4.dp)
            )
        }
    }
}

/**
 * Loading button component for authentication actions.
 */
@Composable
fun AuthButton(
    onClick: () -> Unit,
    text: String,
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    enabled: Boolean = true
) {
    Button(
        onClick = onClick,
        enabled = enabled && !isLoading,
        modifier = modifier.fillMaxWidth()
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(16.dp),
                strokeWidth = 2.dp,
                color = MaterialTheme.colorScheme.onPrimary
            )
            Spacer(modifier = Modifier.width(8.dp))
        }
        Text(text)
    }
}

/**
 * Error message display component.
 */
@Composable
fun ErrorMessage(
    message: String,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Text(
            text = message,
            color = MaterialTheme.colorScheme.onErrorContainer,
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(16.dp)
        )
    }
}
```

### 2. LoginScreen.kt
```kotlin
package eu.torvian.chatbot.app.compose.auth

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import eu.torvian.chatbot.app.viewmodel.auth.AuthViewModel
import org.koin.compose.koinViewModel

/**
 * Login screen for user authentication.
 *
 * @param onNavigateToRegister Callback to navigate to registration screen
 * @param authViewModel ViewModel for authentication operations
 */
@Composable
fun LoginScreen(
    onNavigateToRegister: () -> Unit,
    authViewModel: AuthViewModel = koinViewModel()
) {
    val loginFormState by authViewModel.loginFormState.collectAsState()
    val scrollState = rememberScrollState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp)
            .verticalScroll(scrollState),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // App Title/Logo
        Text(
            text = "Welcome Back",
            style = MaterialTheme.typography.headlineMedium,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Text(
            text = "Sign in to your account",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 32.dp)
        )

        // Login Form
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Username Field
                AuthTextField(
                    value = loginFormState.username,
                    onValueChange = { username ->
                        authViewModel.updateLoginForm(
                            username = username,
                            password = loginFormState.password
                        )
                    },
                    label = "Username or Email",
                    isError = loginFormState.usernameError != null,
                    errorMessage = loginFormState.usernameError,
                    keyboardType = KeyboardType.Email,
                    imeAction = ImeAction.Next,
                    enabled = !loginFormState.isLoading
                )

                // Password Field
                PasswordTextField(
                    value = loginFormState.password,
                    onValueChange = { password ->
                        authViewModel.updateLoginForm(
                            username = loginFormState.username,
                            password = password
                        )
                    },
                    label = "Password",
                    isError = loginFormState.passwordError != null,
                    errorMessage = loginFormState.passwordError,
                    imeAction = ImeAction.Done,
                    onImeAction = {
                        if (loginFormState.isValid) {
                            authViewModel.login(
                                loginFormState.username,
                                loginFormState.password
                            )
                        }
                    },
                    enabled = !loginFormState.isLoading
                )

                // General Error Message
                if (loginFormState.generalError != null) {
                    ErrorMessage(
                        message = loginFormState.generalError,
                        modifier = Modifier.padding(top = 8.dp)
                    )
                }

                // Login Button
                AuthButton(
                    onClick = {
                        authViewModel.login(
                            loginFormState.username,
                            loginFormState.password
                        )
                    },
                    text = "Sign In",
                    isLoading = loginFormState.isLoading,
                    enabled = loginFormState.isValid,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }

        // Register Link
        Row(
            modifier = Modifier.padding(top = 24.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Don't have an account? ",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            TextButton(
                onClick = onNavigateToRegister,
                enabled = !loginFormState.isLoading
            ) {
                Text("Sign Up")
            }
        }
    }
}
```

### 3. RegisterScreen.kt
```kotlin
package eu.torvian.chatbot.app.compose.auth

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import eu.torvian.chatbot.app.viewmodel.auth.AuthViewModel
import org.koin.compose.koinViewModel

/**
 * Registration screen for new user signup.
 *
 * @param onNavigateToLogin Callback to navigate to login screen
 * @param onRegistrationSuccess Callback when registration is successful
 * @param authViewModel ViewModel for authentication operations
 */
@Composable
fun RegisterScreen(
    onNavigateToLogin: () -> Unit,
    onRegistrationSuccess: () -> Unit,
    authViewModel: AuthViewModel = koinViewModel()
) {
    val registerFormState by authViewModel.registerFormState.collectAsState()
    val scrollState = rememberScrollState()

    // Handle successful registration
    LaunchedEffect(registerFormState) {
        if (!registerFormState.isLoading && 
            !registerFormState.hasErrors && 
            registerFormState.username.isNotEmpty() && 
            registerFormState.password.isNotEmpty()) {
            // Registration was successful, navigate to login
            onRegistrationSuccess()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp)
            .verticalScroll(scrollState),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // App Title
        Text(
            text = "Create Account",
            style = MaterialTheme.typography.headlineMedium,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Text(
            text = "Sign up to get started",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 32.dp)
        )

        // Registration Form
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Username Field
                AuthTextField(
                    value = registerFormState.username,
                    onValueChange = { username ->
                        authViewModel.updateRegisterForm(
                            username = username,
                            email = registerFormState.email,
                            password = registerFormState.password,
                            confirmPassword = registerFormState.confirmPassword
                        )
                    },
                    label = "Username",
                    isError = registerFormState.usernameError != null,
                    errorMessage = registerFormState.usernameError,
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Next,
                    enabled = !registerFormState.isLoading
                )

                // Email Field (Optional)
                AuthTextField(
                    value = registerFormState.email,
                    onValueChange = { email ->
                        authViewModel.updateRegisterForm(
                            username = registerFormState.username,
                            email = email,
                            password = registerFormState.password,
                            confirmPassword = registerFormState.confirmPassword
                        )
                    },
                    label = "Email (Optional)",
                    isError = registerFormState.emailError != null,
                    errorMessage = registerFormState.emailError,
                    keyboardType = KeyboardType.Email,
                    imeAction = ImeAction.Next,
                    enabled = !registerFormState.isLoading
                )

                // Password Field
                PasswordTextField(
                    value = registerFormState.password,
                    onValueChange = { password ->
                        authViewModel.updateRegisterForm(
                            username = registerFormState.username,
                            email = registerFormState.email,
                            password = password,
                            confirmPassword = registerFormState.confirmPassword
                        )
                    },
                    label = "Password",
                    isError = registerFormState.passwordError != null,
                    errorMessage = registerFormState.passwordError,
                    imeAction = ImeAction.Next,
                    enabled = !registerFormState.isLoading
                )

                // Confirm Password Field
                PasswordTextField(
                    value = registerFormState.confirmPassword,
                    onValueChange = { confirmPassword ->
                        authViewModel.updateRegisterForm(
                            username = registerFormState.username,
                            email = registerFormState.email,
                            password = registerFormState.password,
                            confirmPassword = confirmPassword
                        )
                    },
                    label = "Confirm Password",
                    isError = registerFormState.confirmPasswordError != null,
                    errorMessage = registerFormState.confirmPasswordError,
                    imeAction = ImeAction.Done,
                    onImeAction = {
                        if (registerFormState.isValid) {
                            authViewModel.register(
                                registerFormState.username,
                                registerFormState.email,
                                registerFormState.password,
                                registerFormState.confirmPassword
                            )
                        }
                    },
                    enabled = !registerFormState.isLoading
                )

                // General Error Message
                if (registerFormState.generalError != null) {
                    ErrorMessage(
                        message = registerFormState.generalError,
                        modifier = Modifier.padding(top = 8.dp)
                    )
                }

                // Register Button
                AuthButton(
                    onClick = {
                        authViewModel.register(
                            registerFormState.username,
                            registerFormState.email,
                            registerFormState.password,
                            registerFormState.confirmPassword
                        )
                    },
                    text = "Create Account",
                    isLoading = registerFormState.isLoading,
                    enabled = registerFormState.isValid,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }

        // Login Link
        Row(
            modifier = Modifier.padding(top = 24.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Already have an account? ",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            TextButton(
                onClick = onNavigateToLogin,
                enabled = !registerFormState.isLoading
            ) {
                Text("Sign In")
            }
        }
    }
}
```

### 4. AuthLoadingScreen.kt
```kotlin
package eu.torvian.chatbot.app.compose.auth

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp

/**
 * Loading screen shown during authentication state checking.
 */
@Composable
fun AuthLoadingScreen() {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // App Logo/Title
        Text(
            text = "Chatbot",
            style = MaterialTheme.typography.headlineLarge,
            color = MaterialTheme.colorScheme.primary,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 32.dp)
        )

        // Loading Indicator
        CircularProgressIndicator(
            modifier = Modifier.size(48.dp),
            strokeWidth = 4.dp
        )

        // Loading Message
        Text(
            text = "Checking authentication...",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 16.dp)
        )
    }
}
```

### 5. AuthErrorScreen.kt
```kotlin
package eu.torvian.chatbot.app.compose.auth

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Error
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp

/**
 * Error screen shown when authentication initialization fails.
 *
 * @param message Error message to display
 * @param onRetry Callback to retry authentication
 * @param onGoToLogin Callback to navigate to login screen
 */
@Composable
fun AuthErrorScreen(
    message: String,
    onRetry: () -> Unit,
    onGoToLogin: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Error Icon
        Icon(
            imageVector = Icons.Default.Error,
            contentDescription = "Error",
            tint = MaterialTheme.colorScheme.error,
            modifier = Modifier
                .size(64.dp)
                .padding(bottom = 16.dp)
        )

        // Error Title
        Text(
            text = "Authentication Error",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.error,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        // Error Message
        Text(
            text = message,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 32.dp)
        )

        // Action Buttons
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Button(
                onClick = onRetry,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Retry")
            }

            OutlinedButton(
                onClick = onGoToLogin,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Go to Login")
            }
        }
    }
}
```

## Key Design Features

### Material 3 Design System
- Uses Material 3 components and theming
- Consistent spacing and typography
- Proper color scheme integration
- Elevation and surface handling

### User Experience
- **Form Validation**: Real-time error clearing
- **Loading States**: Visual feedback during operations
- **Keyboard Navigation**: Proper IME actions and focus management
- **Accessibility**: Proper content descriptions and semantic structure

### Error Handling
- **Field-specific errors**: Individual validation messages
- **General errors**: API and network error display
- **Loading states**: Disabled interactions during operations

### Responsive Design
- **Scrollable content**: Handles different screen sizes
- **Flexible layouts**: Adapts to content and screen constraints
- **Touch targets**: Proper sizing for interactive elements

## Integration Notes

### ViewModel Integration
All screens integrate seamlessly with the AuthViewModel from Task 1, using:
- `collectAsState()` for reactive UI updates
- Form state management through ViewModel methods
- Proper error handling and loading states

### Navigation
Screens accept navigation callbacks as parameters, making them flexible for different navigation implementations.

### Theming
All components use Material 3 theming, ensuring consistency with the rest of the application.

## Usage Examples

### Integration with Navigation
```kotlin
// In AppShell.kt or navigation setup
@Composable
fun AuthenticationFlow() {
    val navController = rememberNavController()

    NavHost(navController = navController, startDestination = Login) {
        composable<Login> {
            LoginScreen(
                onNavigateToRegister = {
                    navController.navigate(Register) {
                        // Clear login from back stack
                        popUpTo(Login) { inclusive = true }
                    }
                }
            )
        }

        composable<Register> {
            RegisterScreen(
                onNavigateToLogin = {
                    navController.navigate(Login) {
                        popUpTo(Register) { inclusive = true }
                    }
                },
                onRegistrationSuccess = {
                    navController.navigate(Login) {
                        popUpTo(Register) { inclusive = true }
                    }
                }
            )
        }
    }
}
```

### Testing Considerations

#### Preview Functions
```kotlin
// Add to each screen file for Compose previews
@Preview
@Composable
fun LoginScreenPreview() {
    MaterialTheme {
        LoginScreen(
            onNavigateToRegister = {},
            authViewModel = previewAuthViewModel()
        )
    }
}

@Composable
private fun previewAuthViewModel(): AuthViewModel {
    // Mock ViewModel for previews
    return mockk<AuthViewModel>()
}
```

#### Unit Testing
- Test form validation logic in isolation
- Test error state handling
- Test navigation callbacks
- Mock AuthViewModel for UI testing

#### Integration Testing
- Test complete authentication flows
- Test error scenarios with real ViewModel
- Test keyboard navigation and accessibility

## Customization Options

### Theming
All components respect Material 3 theming. Customize colors, typography, and shapes through your app's theme:

```kotlin
// Custom colors for authentication screens
val AuthColors = lightColorScheme(
    primary = Color(0xFF1976D2),
    error = Color(0xFFD32F2F),
    // ... other colors
)
```

### Branding
Replace the app title and add logos by modifying the header sections in each screen.

### Form Fields
Add or remove form fields by:
1. Updating the form state classes in AuthFormState.kt
2. Adding validation in AuthFormValidation.kt
3. Updating the ViewModel methods
4. Adding UI components in the screens

## Performance Considerations

### State Management
- Uses `collectAsState()` for efficient recomposition
- Form state updates are optimized to minimize recomposition
- Loading states prevent unnecessary user interactions

### Memory Usage
- Screens are stateless and rely on ViewModel for state
- No memory leaks from retained references
- Proper cleanup through ViewModel lifecycle

### Accessibility
- All interactive elements have proper content descriptions
- Form fields support screen readers
- Error messages are announced to accessibility services
- Proper focus management for keyboard navigation

This implementation provides a complete, production-ready authentication UI that follows modern Android development best practices and integrates perfectly with the existing codebase architecture.
