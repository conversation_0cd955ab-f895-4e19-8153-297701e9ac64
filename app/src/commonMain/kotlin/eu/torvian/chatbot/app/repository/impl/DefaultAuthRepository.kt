package eu.torvian.chatbot.app.repository.impl

import arrow.core.Either
import arrow.core.raise.either
import arrow.core.raise.withError
import eu.torvian.chatbot.app.repository.AuthRepository
import eu.torvian.chatbot.app.repository.AuthState
import eu.torvian.chatbot.app.repository.RepositoryError
import eu.torvian.chatbot.app.repository.toRepositoryError
import eu.torvian.chatbot.app.service.api.AuthApi
import eu.torvian.chatbot.app.service.auth.AuthenticationFailureEvent
import eu.torvian.chatbot.app.service.auth.TokenStorage
import eu.torvian.chatbot.app.service.misc.EventBus
import eu.torvian.chatbot.app.utils.misc.kmpLogger
import eu.torvian.chatbot.common.models.User
import eu.torvian.chatbot.common.models.auth.LoginRequest
import eu.torvian.chatbot.common.models.auth.RegisterRequest
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * Default implementation of the AuthRepository interface.
 *
 * This repository manages authentication state using StateFlow and coordinates
 * between the AuthApi for server operations and TokenStorage for local token management.
 *
 * @property authApi The API client for authentication operations
 * @property tokenStorage The storage for managing authentication tokens
 */
class DefaultAuthRepository(
    private val authApi: AuthApi,
    private val tokenStorage: TokenStorage,
    private val eventBus: EventBus
) : AuthRepository {

    companion object {
        private val logger = kmpLogger<DefaultAuthRepository>()
    }

    private val _authState = MutableStateFlow<AuthState>(AuthState.Unauthenticated)
    override val authState: StateFlow<AuthState> = _authState.asStateFlow()

    private val repositoryScope = CoroutineScope(Dispatchers.Default)

    init {
        repositoryScope.launch {
            eventBus.events.collect { event ->
                if (event is AuthenticationFailureEvent) {
                    logger.warn("Received AuthenticationFailureEvent: ${event.reason}.")
                    _authState.value = AuthState.Unauthenticated
                }
            }
        }
    }

    override suspend fun login(request: LoginRequest): Either<RepositoryError, Unit> = either {
        _authState.value = AuthState.Loading

        // Perform login API call
        val loginResponse = withError({ apiError ->
            _authState.value = AuthState.Unauthenticated
            apiError.toRepositoryError("Login failed")
        }) {
            authApi.login(request).bind()
        }

        // Save tokens
        withError({ tokenError ->
            _authState.value = AuthState.Unauthenticated
            RepositoryError.OtherError("Failed to save tokens after successful login: ${tokenError.message}")
        }) {
            tokenStorage.saveTokens(
                accessToken = loginResponse.accessToken,
                refreshToken = loginResponse.refreshToken,
                expiresAt = loginResponse.expiresAt
            ).bind()
        }

        // Update auth state on successful token save
        _authState.value = AuthState.Authenticated(
            userId = loginResponse.user.id,
            username = loginResponse.user.username
        )
    }

    override suspend fun register(request: RegisterRequest): Either<RepositoryError, User> = either {
        withError({ apiError ->
            apiError.toRepositoryError("Registration failed")
        }) {
            authApi.register(request).bind()
        }
    }

    override suspend fun logout(): Either<RepositoryError, Unit> = either {
        withError({ apiError ->
            apiError.toRepositoryError("Logout failed")
        }) {
            authApi.logout().bind()
        }
        tokenStorage.clearTokens()
            .onLeft {
                logger.warn("Failed to clear tokens on logout: ${it.message}")
            }
        _authState.value = AuthState.Unauthenticated
    }

    override suspend fun isAuthenticated(): Boolean {
        return _authState.value is AuthState.Authenticated
    }
}
